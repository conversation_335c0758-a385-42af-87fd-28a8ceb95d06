import { Injectable } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { Subject } from 'rxjs';
import { Cancel, COMMON_SELECT_FILTER, SalesOrderAssociationHeader, Submit, TransferOrderResource } from 'src/app/app.constants';
import { RoleResponse } from 'src/app/model/Role/roleResponse.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { CustomerAssociationModelRequest } from 'src/app/model/customer-association-model/customer-association-model-request.model';
import { DeviceOperationService } from '../../FeatureModule/Device/DeviceService/Device-Operation/device-operation.service';
import { KitManagemantService } from '../Service/KitManagemant/kit-managemant.service';
import { OtsKitManagemantService } from '../Service/KitManagemant/ots-kit-managemant.service';
import { RoleApiCallService } from '../Service/RoleService/role-api-call.service';
import { BridgeKitOperationsEnum } from '../enum/Operations/BridgeKitOperations.enum';
import { DeviceListOperations } from '../enum/Operations/DeviceListOperations.enum';
import { OTSKitOperations } from '../enum/Operations/OTSKitOperations.enum';
import { ProbeOperationsEnum } from '../enum/Operations/ProbeOperations.enum';
import { RoleOperationsEnum } from '../enum/Operations/RoleOperations.enum';
import { SalesOrderOperationsEnum } from '../enum/Operations/SalesOrderOperations.enum';
import { PermissionAction } from '../enum/Permission/permissionAction.enum';
import { ImportCsvfileService } from '../importFileService/import-csvfile.service';
import { CreateUpdateRoleService } from '../modalservice/Role/create-update-role.service';
import { CustomerAssociationService } from '../modalservice/customer-association.service';
import { PermissionService } from '../permission.service';

@Injectable({
  providedIn: 'root'
})
export class CommonOperationsService {

  //loading
  private commonLoadingSubject = new Subject<boolean>();

  constructor(private toste: ToastrService,
    private roleApiCallService: RoleApiCallService,
    private createUpdateRoleService: CreateUpdateRoleService,
    private permissionService: PermissionService,
    private importCsvfileService: ImportCsvfileService,
    private kitManagemantService: KitManagemantService,
    private otsKitManagemantService: OtsKitManagemantService) { }

  /**
   * <AUTHOR>
   * @returns Role List Operations
   */
  public accessRoleOperations(): string[] {
    let operationsList = [];
    for (let operation in RoleOperationsEnum) {
      if ((RoleOperationsEnum.UpdateRole == RoleOperationsEnum[operation] && this.permissionService.getRolePermission(PermissionAction.UPDATE_ROLE_ACTION)) ||
        (RoleOperationsEnum.DeleteRole == RoleOperationsEnum[operation] && this.permissionService.getRolePermission(PermissionAction.DELETE_ROLE_ACTION)) ||
        (RoleOperationsEnum.RoleOperations == RoleOperationsEnum[operation])) {
        operationsList.push(RoleOperationsEnum[operation]);
      }
    }
    return operationsList;
  }

  /**
  * <AUTHOR>
  * @returns Sales Order List Operations
  */
  public accessSalesOrderOperations(manualSync: boolean, transferOrder: boolean): string[] {
    let operationsList = [];
    for (let operation in SalesOrderOperationsEnum) {
      if ((SalesOrderOperationsEnum.MANUAL_SYNC == SalesOrderOperationsEnum[operation] && this.permissionService.getSalesOrderPermission(PermissionAction.SALES_ORDER_MANUAL_SYNC_ACTION) && manualSync) ||
        (SalesOrderOperationsEnum.DELETE_ORDER == SalesOrderOperationsEnum[operation] && this.permissionService.getSalesOrderPermission(PermissionAction.SALES_ORDER_DELETE_ACTION)) ||
        (SalesOrderOperationsEnum.TRANSFER_SALES_ORDER == SalesOrderOperationsEnum[operation] && this.permissionService.getSalesOrderPermission(PermissionAction.SALES_ORDER_TRANSFER_ORDER_ACTION) && transferOrder) ||
        (SalesOrderOperationsEnum.SALES_ORDER_OPERATIONS == SalesOrderOperationsEnum[operation])) {
        operationsList.push(SalesOrderOperationsEnum[operation]);
      }
    }
    return operationsList;
  }

  /**
  * <AUTHOR>
  * @returns Failed Sales Order List Operations
  */
  public failedSalesOrderOperations(): string[] {
    let operationsList = [];
    for (let operation in SalesOrderOperationsEnum) {
      if ((SalesOrderOperationsEnum.MANUAL_SYNC == SalesOrderOperationsEnum[operation] && this.permissionService.getSalesOrderPermission(PermissionAction.SALES_ORDER_MANUAL_SYNC_ACTION)) ||
        (SalesOrderOperationsEnum.SALES_ORDER_OPERATIONS == SalesOrderOperationsEnum[operation])) {
        operationsList.push(SalesOrderOperationsEnum[operation]);
      }
    }
    return operationsList;
  }



  /**
   * Kit Operation
   * 
   * <AUTHOR>
   * @returns 
   */
  public accessKitOperations(isImportCsv: boolean): string[] {
    let operationsList: Array<string> = [];
    for (let operation in BridgeKitOperationsEnum) {
      if ((BridgeKitOperationsEnum.ImportCSV == BridgeKitOperationsEnum[operation] && isImportCsv &&
        this.permissionService.getKitManagementPermission(PermissionAction.IMPORT_BRIDGE_KIT_CSV_ACTION)) ||
        (BridgeKitOperationsEnum.BrigeKitOperations == BridgeKitOperationsEnum[operation])) {
        operationsList.push(BridgeKitOperationsEnum[operation]);
      }
    }
    return operationsList;
  }

  /**
 * Ots Kit Operation
 * 
 * <AUTHOR>
 * @returns 
 */
  public accessOTSKitOperations(isImportCsv: boolean): string[] {
    let operationsList: Array<string> = [];
    for (let operation in OTSKitOperations) {
      if ((OTSKitOperations.ImportCSV == OTSKitOperations[operation] && isImportCsv &&
        this.permissionService.getKitManagementPermission(PermissionAction.IMPORT_OTS_KIT_CSV_ACTION)) ||
        (OTSKitOperations.KitOperations == OTSKitOperations[operation])) {
        operationsList.push(OTSKitOperations[operation]);
      }
    }
    return operationsList;
  }

  public showEmptyFilterTosteMessge(): void {
    this.toste.info(COMMON_SELECT_FILTER);
  }

  /**
  * Role Operation Action
  * @param operationName 
  * @param resourceName 
  * @param roleObject 
  */
  public changeOperationForRole(operationName: string, resourceName: string, selectedRoleIdList: number[], isFilterHidden: boolean, roleResponseList: RoleResponse[]): void {
    if (selectedRoleIdList.length == 0) {
      this.toste.info("Please Select Role(s)");
    } else {
      switch (operationName) {
        case RoleOperationsEnum.DeleteRole:
          this.deleteRoleOperation(resourceName, selectedRoleIdList, isFilterHidden, roleResponseList);
          break;
        case RoleOperationsEnum.UpdateRole:
          this.updateRoleOperation(resourceName, selectedRoleIdList, isFilterHidden, roleResponseList);
          break;
        default:
          break;
      }
    }
    this.setDefaltOperationDrpValue("roleOperation", RoleOperationsEnum.RoleOperations);
  }

  /**
   * Delete Role Operation
   * 
   * <AUTHOR>
   * @param resourceName 
   * @param selectedRoleIdList 
   * @param isFilterHidden 
   * @param roleResponseList 
   */
  private deleteRoleOperation(resourceName: string, selectedRoleIdList: number[], isFilterHidden: boolean, roleResponseList: RoleResponse[]): void {
    this.roleApiCallService.deleteRoleConfirmation(selectedRoleIdList, resourceName, isFilterHidden);
  }

  /**
   * Update Role Operation
   * 
   * <AUTHOR>
   * @param resourceName 
   * @param selectedRoleIdList 
   * @param isFilterHidden 
   * @param roleResponseList 
   */
  private updateRoleOperation(resourceName: string, selectedRoleIdList: number[], isFilterHidden: boolean, roleResponseList: RoleResponse[]) {
    if (selectedRoleIdList.length == 1) {
      this.createUpdateRoleService.openAddUpdateRolePopup(this.createUpdateRoleService.getUpdateRoleParameter(selectedRoleIdList[0], resourceName, isFilterHidden))
        .finally(() => { });
    } else if (selectedRoleIdList.length > 1) {
      this.toste.info("Please Select one Role");
    }
  }

  /**
  * Sets the default value of a dropdown element.
  *
  * This method selects a specified value for a dropdown element by its ID.
  * If the element is not found or is null/undefined, no action is performed.
  *
  * @param eleId - The ID of the HTML select element.
  * @param value - The value to be set as selected in the dropdown.
  */
  public setDefaltOperationDrpValue(eleId: string, value: string): void {
    let selection = document.getElementById(eleId) as HTMLSelectElement;
    if (!isNullOrUndefined(selection)) {
      selection.value = value;
    }
  }

  /**
   * get common loading subject
   * @returns 
   */
  public getCommonLoadingSubject(): Subject<boolean> {
    return this.commonLoadingSubject;
  }

  /**
   * call common loading subject
   * @param status 
   */
  public callCommonLoadingSubject(status: boolean): void {
    this.commonLoadingSubject.next(status);
  }

  /**
   * Kit managemant Opeartions
   * 
   * <AUTHOR>
   * @param operationName 
   */
  public commonOperationForKitManagement(operationName: string, listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, resourceName: string, isFilterHidden: boolean) {
    if (operationName == BridgeKitOperationsEnum.ImportCSV) {
      this.importCsvfileService.openImportCSVModel(this.importCsvfileService.getImportFilePopupInputForKitManagement())
        .then(() => {
          this.kitManagemantService.callRefreshPageSubject(listingPageReloadSubjectParameter, resourceName, isFilterHidden);
        }, () => { });
    }
    this.setDefaltOperationDrpValue("bridgeKitOperation", BridgeKitOperationsEnum.BrigeKitOperations);
  }

  /**
 * OTS Kit managemant Opeartions
 * 
 * <AUTHOR>
 * @param operationName 
 */
  public commonOperationForOTSKitManagement(operationName: string, listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, resourceName: string, isFilterHidden: boolean): void {
    if (operationName == OTSKitOperations.ImportCSV) {
      this.importCsvfileService.openImportCSVModel(this.importCsvfileService.getImportFilePopupInputForOTSKitManagement())
        .then(() => {
          this.otsKitManagemantService.callOTSKitRefreshPageSubject(listingPageReloadSubjectParameter, resourceName, isFilterHidden);
        }, () => { });
    }
    this.setDefaltOperationDrpValue("otsKitOperation", OTSKitOperations.KitOperations);
  }


  /**
  * Get Devise List Operations
  * 
  * @returns 
  */
  public accessDeviceListOperations(exportCsvActionDisplay: boolean, transferProductDisplay: boolean, resourceName: string): string[] {
    const permissions = {
      lock: this.permissionService.getDevicePermission(PermissionAction.LOCK_DEVICE_ACTION),
      update: this.permissionService.getDevicePermission(PermissionAction.UPDATE_DEVICE_TYPE_ACTION),
      associate: this.permissionService.getDevicePermission(PermissionAction.ASSOCIATE_CUSTOMER_TO_DEVICE_ACTION),
      read: this.permissionService.getDevicePermission(PermissionAction.GET_DEVICE_ACTION),
      edit: this.permissionService.getDevicePermission(PermissionAction.EDITABLE_DEVICE_ACTION),
      disable: this.permissionService.getDevicePermission(PermissionAction.DISABLE_DEVICE_ACTION),
      rma: this.permissionService.getDevicePermission(PermissionAction.RMA_DEVICE_ACTION),
      transfer: this.permissionService.getDevicePermission(PermissionAction.TRANSFER_DEVICE_ACTION)
    };

    return resourceName === TransferOrderResource
      ? this.getTransferOrderDeviceOperations(permissions)
      : this.getDefaultOperations(permissions, exportCsvActionDisplay, transferProductDisplay);
  }

  /**
  * Get Transfer Order Device Operations
  * 
  * <AUTHOR>
  * @param permissions 
  * @returns 
  */
  private getTransferOrderDeviceOperations(permissions: any): string[] {
    const operations = [];

    operations.push(DeviceListOperations.DeviceOperations);
    if (permissions.edit) operations.push(DeviceListOperations.EDIT_DISABLE_DEVICE);

    return operations;
  }

  /**
  * Get Default Device List Operations
  *
  * <AUTHOR>
  * @param permissions
  * @param exportCsvActionDisplay
  * @param transferProductDisplay
  * @returns
  */
  private getDefaultOperations(permissions: any, exportCsvActionDisplay: boolean, transferProductDisplay: boolean): string[] {
    const operations = [];

    operations.push(DeviceListOperations.DeviceOperations);
    if (permissions.edit) operations.push(DeviceListOperations.EDIT_ENABLE_DEVICE, DeviceListOperations.EDIT_DISABLE_DEVICE);
    if (permissions.lock) operations.push(DeviceListOperations.UNLOCK_DEVICES, DeviceListOperations.LOCK_DEVICES);
    if (permissions.update) operations.push(DeviceListOperations.SET_DEVICE_TO_TEST, DeviceListOperations.SET_DEVICE_TO_CLIENT, DeviceListOperations.SET_DEVICE_TO_DEMO);
    if (permissions.disable) operations.push(DeviceListOperations.DISABLED_DEVICES);
    if (permissions.rma) operations.push(DeviceListOperations.RMA_DEVICES);
    if (permissions.associate) operations.push(DeviceListOperations.CUSTOMER_SALES_ORDER_ASSOCIATION);
    if (permissions.read && exportCsvActionDisplay) operations.push(DeviceListOperations.Export_CSV);
    if (permissions.transfer && transferProductDisplay) operations.push(DeviceListOperations.TRANSFER_DEVICE);

    return operations;
  }

  /**
  * Get Probe Operation List
  * 
  * <AUTHOR>
  * @param exportCsvActionDisplay 
  * @param historicalConnectionsDisplay 
  * @param tranferProbeDisplay
  * @param showProbeEditable 
  * @returns 
  */
  public accessProbeOperations(exportCsvActionDisplay: boolean, historicalConnectionsDisplay: boolean, tranferProbeDisplay: boolean, resourceName: string): string[] {
    const permissions = {
      delete: this.permissionService.getProbPermission(PermissionAction.DELETE_PROB_ACTION),
      updateType: this.permissionService.getProbPermission(PermissionAction.UPDATE_PROB_TYPE_ACTION),
      associateCustomer: this.permissionService.getProbPermission(PermissionAction.ASSOCIATE_CUSTOMER_TO_PROBE_ACTION),
      assignFeature: this.permissionService.getProbPermission(PermissionAction.ASSIGN_FEATURE_TO_PROBE_ACTION),
      downloadLicense: this.permissionService.getProbPermission(PermissionAction.DOWNLOAD_PROB_FEATURE_LICENSE_ACTION),
      disable: this.permissionService.getProbPermission(PermissionAction.DISABLE_PROBE_ACTION),
      editEnableDisable: this.permissionService.getProbPermission(PermissionAction.EDITABLE_PROBE_ACTION),
      rma: this.permissionService.getProbPermission(PermissionAction.RMA_PROBE_ACTION),
      read: this.permissionService.getProbPermission(PermissionAction.GET_PROB_ACTION),
      lockUnlock: this.permissionService.getProbPermission(PermissionAction.LOCK_DEVICE_ACTION),
      transfer: this.permissionService.getProbPermission(PermissionAction.TRANSFER_PROBE_ACTION)
    };

    return resourceName === TransferOrderResource
      ? this.getTransferOrderProbeOperations(permissions)
      : this.getDefaultProbeOperations(permissions, exportCsvActionDisplay, historicalConnectionsDisplay, tranferProbeDisplay);
  }

  /**
  * Get Transfer Order Probe Operations
  * 
  * <AUTHOR>
  * @param permissions 
  * @returns 
  */
  private getTransferOrderProbeOperations(permissions: any): string[] {
    const operations = [];

    operations.push(ProbeOperationsEnum.Probe_Operations);
    if (permissions.editEnableDisable) operations.push(ProbeOperationsEnum.EDIT_ENABLE_PROBE);

    return operations;
  }

  /**
  * Get Default Probe Operations
  * 
  * <AUTHOR>
  * @param permissions 
  * @param exportCsvActionDisplay 
  * @param historicalConnectionsDisplay 
  * @param tranferProbeDisplay 
  * @returns 
  */
  private getDefaultProbeOperations(permissions: any, exportCsvActionDisplay: boolean, historicalConnectionsDisplay: boolean, tranferProbeDisplay: boolean): string[] {
    const operations = [];

    operations.push(ProbeOperationsEnum.Probe_Operations);
    if (permissions.editEnableDisable) operations.push(ProbeOperationsEnum.EDIT_ENABLE_PROBE, ProbeOperationsEnum.EDIT_DISABLE_PROBE);
    if (permissions.lockUnlock) operations.push(ProbeOperationsEnum.UNLOCK_PROBES, ProbeOperationsEnum.LOCK_PROBES);
    if (permissions.assignFeature) operations.push(ProbeOperationsEnum.ASSIGN_FEATURES_TO_PROBE);
    if (permissions.associateCustomer) operations.push(ProbeOperationsEnum.CUSTOMER_ASSOCIATION);
    if (permissions.downloadLicense) operations.push(ProbeOperationsEnum.DOWNLOAD_PROBES);
    if (permissions.delete) operations.push(ProbeOperationsEnum.DELETE_PROBES);
    if (permissions.disable) operations.push(ProbeOperationsEnum.DISABLED_PROBE);
    if (permissions.rma) operations.push(ProbeOperationsEnum.RMA_PROBE);
    if (permissions.updateType) operations.push(ProbeOperationsEnum.UPDATE_PROBE_TYPE);
    if (permissions.read && exportCsvActionDisplay) operations.push(ProbeOperationsEnum.Export_CSV);
    if (historicalConnectionsDisplay) operations.push(ProbeOperationsEnum.EXPORT_HISTORICAL_CONNECTIONS);
    if (permissions.transfer && tranferProbeDisplay) operations.push(ProbeOperationsEnum.TRANSFER_PROBE);

    return operations;
  }

}
