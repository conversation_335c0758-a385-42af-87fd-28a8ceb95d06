import { Component, OnInit } from '@angular/core';
import { ProbListResource } from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { ProbeListFilterRequestBody } from 'src/app/model/probe/ProbeListFilterRequestBody.model';
import { ProbeOperationService } from '../ProbeService/Probe-Operation/probe-operation.service';

@Component({
  selector: 'app-probe-module',
  templateUrl: './probe-module.component.html',
  styleUrl: './probe-module.component.css'
})
export class ProbeModuleComponent implements OnInit {

  isProbeListingPageDisplay: boolean = true;
  isProbeDetailPageDisplay: boolean = false;

  loading: boolean = false;

  probeIdInput: number;
  probeListResource = ProbListResource;

  // Properties for filter component initialization
  listPageRefreshForbackToDetailPage: boolean = false;

  constructor(private readonly probeOperationService: ProbeOperationService) { }

  public ngOnInit(): void {
    this.setDefaultState();
  }

  public ngOnDestroy(): void {
    this.setDefaultState();
  }

  private setDefaultState() {
    this.probeOperationService.setProbeSearchRequestBodyForListingApi(null);
    this.probeOperationService.setIsFilterHiddenForListing(false);
    this.probeOperationService.setListPageRefreshForbackToOtherPage(false);
  }

  public showProbeDetail(probeId: number) {
    this.probeIdInput = probeId;
    this.isProbeDetailPageDisplay = true;
    this.isProbeListingPageDisplay = false;
  }

  /**
  * Show probe listing page from detail page
  * <AUTHOR>
  */
  public showProbe(): void {
    this.isProbeListingPageDisplay = true;
    this.isProbeDetailPageDisplay = false;
    this.probeOperationService.setListPageRefreshForbackToOtherPage(true);
  }

  public getProbeId(probeId: number): void {
    this.probeIdInput = probeId;
  }

}
