import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { DeviceSearchRequest } from 'src/app/model/device/deviceSearchRequest.model';
import { DeviceOperationService } from '../DeviceService/Device-Operation/device-operation.service';
import { DeviceModuleComponent } from './device-module.component';

describe('DeviceModuleComponent', () => {
  let component: DeviceModuleComponent;
  let fixture: ComponentFixture<DeviceModuleComponent>;
  let deviceOperationServiceSpy: jasmine.SpyObj<DeviceOperationService>;

  beforeEach(async () => {
    const spy = jasmine.createSpyObj('DeviceOperationService', ['callRefreshPageSubject', 'setDeviceSearchRequestBodyForListingApi', 'setIsFilterHiddenForListing', 'setListPageRefreshForbackToOtherPage', 'getListPageRefreshForbackToOtherPage', 'getDeviceSearchRequestBodyForListingApi', 'getIsFilterHiddenForListing',]);

    await TestBed.configureTestingModule({
      declarations: [DeviceModuleComponent],
      providers: [
        { provide: DeviceOperationService, useValue: spy }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    })
      .compileComponents();

    fixture = TestBed.createComponent(DeviceModuleComponent);
    component = fixture.componentInstance;
    deviceOperationServiceSpy = TestBed.inject(DeviceOperationService) as jasmine.SpyObj<DeviceOperationService>;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('showDevice', () => {
    it('should show device listing page and hide detail page', () => {
      component.isDeviceListingPageDisplay = false;
      component.isDeviceDetailPageDisplay = true;
      component.showDevice();
      expect(component.isDeviceListingPageDisplay).toBe(true);
      expect(component.isDeviceDetailPageDisplay).toBe(false);
    });
  });

  describe('showDeviceDetail', () => {
    it('should show device detail page and hide listing page', () => {
      component.isDeviceListingPageDisplay = true;
      component.isDeviceDetailPageDisplay = false;
      component.showDeviceDetail();
      expect(component.isDeviceListingPageDisplay).toBe(false);
      expect(component.isDeviceDetailPageDisplay).toBe(true);
    });
  });

  describe('getDeviceId', () => {
    it('should set deviceIdInput when called', () => {
      const testDeviceId = 123;
      component.getDeviceId(testDeviceId);
      expect(component.deviceIdInput).toBe(testDeviceId);
    });
  });

});
