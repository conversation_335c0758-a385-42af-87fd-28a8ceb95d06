import { DatePipe } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService } from 'ngx-webstorage';
import { Subject } from 'rxjs';
import { ProbeFilterAction } from 'src/app/model/probe/ProbeFilterAction.model';
import { commonsProviders } from '../../../Tesing-Helper/test-utils';
import { DateTimeDisplayFormat, ITEMS_PER_PAGE, ProbListResource, SHOW_ENTRY } from '../../../app.constants';
import { ConfirmDialogService } from '../../CommonComponent/confirmationdialog/confirmation.service';
import { ListingPageReloadSubjectParameter } from '../../../model/common/listingPageReloadSubjectParameter.model';
import { ProbeListFilterRequestBody } from '../../../model/probe/ProbeListFilterRequestBody.model';
import { ProbeDetailResponse } from '../../../model/probe/probeDetail.model';
import { ExceptionHandlingService } from '../../../shared/ExceptionHandling.service';
import { CountryCacheService } from '../../../shared/Service/CacheService/countrycache.service';
import { JobService } from '../../../shared/Service/JobService/job.service';
import { PresetApiService } from '../../../shared/Service/PresetService/preset-api.service';
import { ProbeApiService } from '../../../shared/Service/ProbeService/probe-api.service';
import { SalesOrderApiCallService } from '../../../shared/Service/SalesOrderService/sales-order-api-call.service';
import { AuthJwtService } from '../../../shared/auth-jwt.service';
import { DeviceService } from '../../../shared/device.service';
import { ProductStatusEnum } from '../../../shared/enum/Common/ProductStatus.enum';
import { ProbeOperationsEnum } from '../../../shared/enum/Operations/ProbeOperations.enum';
import { DeviceHistoricalData } from '../../../shared/enum/Probe/DeviceHistoricalData.enum';
import { collapseFilterTextEnum } from '../../../shared/enum/collapseFilterButtonText.enum';
import { CustomerAssociationService } from '../../../shared/modalservice/customer-association.service';
import { SalesOrderPdfDownloadService } from '../../../shared/modalservice/salesOrderPdf/sales-order-pdf-download.service';
import { PermissionService } from '../../../shared/permission.service';
import { EnumMappingDisplayNamePipe } from '../../../shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { AssignConfigDisablePipe } from '../../../shared/pipes/Probe/assign-config-disable.pipe';
import { FeatureValidityOptionHideShowPipe } from '../../../shared/pipes/Probe/feature-validity-option-hide-show.pipe';
import { FeaturesBaseResponseDisplayPipe } from '../../../shared/pipes/Probe/features-base-response-display.pipe';
import { FeaturesCheckBoxPipe } from '../../../shared/pipes/Probe/features-checkbox.pipe';
import { FeaturesCustomEndDateDisplayPipe } from '../../../shared/pipes/Probe/features-customEndDateDisplay.pipe';
import { FeaturesExpireDateDisplayPipe } from '../../../shared/pipes/Probe/features-expire-datedisplay.pipe';
import { FeaturesRadioButtonPipe } from '../../../shared/pipes/Probe/features-radio-button.pipe';
import { FeaturesTextDisplayPipe } from '../../../shared/pipes/Probe/features-textdisplay.pipe';
import { FeaturesValidityPartNumberDisplayPipe } from '../../../shared/pipes/Probe/features-validity-partNumber-Display.pipe';
import { CommonBooleanValueDisplayPipe } from '../../../shared/pipes/common-boolean-value-display.pipe';
import { UpdateAssociationService } from '../../../shared/update-association.service';
import { CommonOperationsService } from '../../../shared/util/common-operations.service';
import { CommonsService } from '../../../shared/util/commons.service';
import { DownloadService } from '../../../shared/util/download.service';
import { KeyValueMappingServiceService } from '../../../shared/util/key-value-mapping-service.service';
import { TransferOrderModuleComponent } from '../../TransferOrder/transfer-order-module/transfer-order-module.component';
import { ProbeOperationService } from '../ProbeService/Probe-Operation/probe-operation.service';
import { CreateUpdateMultipleProbeComponent } from '../create-update-multiple-probe/create-update-multiple-probe.component';
import { OtsProbesDetailComponent } from '../ots-probes-detail/ots-probes-detail.component';
import { OtsProbesFilterComponent } from '../ots-probes-filter/ots-probes-filter.component';
import { OtsProbesComponent } from './ots-probes-list.component';

describe('OtsProbesComponent', () => {
  let component: OtsProbesComponent;
  let fixture: ComponentFixture<OtsProbesComponent>;
  let localStorageServiceMock: jasmine.SpyObj<LocalStorageService>;
  let authServiceSpy: jasmine.SpyObj<AuthJwtService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;
  let probeApiServiceSpy: jasmine.SpyObj<ProbeApiService>;
  let salesOrderApiCallServiceSpy: jasmine.SpyObj<SalesOrderApiCallService>;
  let countryCacheServiceSpy: jasmine.SpyObj<CountryCacheService>;
  let presetApiServiceSpy: jasmine.SpyObj<PresetApiService>;
  let customerAssociationServicespy: jasmine.SpyObj<CustomerAssociationService>;
  let confirmDialogServiceMock: jasmine.SpyObj<ConfirmDialogService>;
  let updateAssociationServiceMock: jasmine.SpyObj<UpdateAssociationService>;
  let routerSpy: jasmine.SpyObj<Router>;
  let commonsServiceSpy: jasmine.SpyObj<CommonsService>;
  let commonOperationsServiceSpy: jasmine.SpyObj<CommonOperationsService>;
  let downloadServiceSpy: jasmine.SpyObj<DownloadService>;
  let salesOrderPdfDownloadServiceSpy: jasmine.SpyObj<SalesOrderPdfDownloadService>;
  let probeOperationServiceSpy: jasmine.SpyObj<ProbeOperationService>;
  let keyValueMappingServiceSpy: jasmine.SpyObj<KeyValueMappingServiceService>;
  let deviceServiceSpy: jasmine.SpyObj<DeviceService>;
  let jobServiceSpy: jasmine.SpyObj<JobService>;

  // Mock subjects
  let commonLoadingSubject: Subject<boolean>;
  let isLoadingSubject: Subject<boolean>;
  let downloadZipFileSubject: Subject<boolean>;
  let probeListFilterSubject: Subject<ProbeFilterAction>;
  let probeListLoadingSubject: Subject<boolean>;

  const content: Array<ProbeDetailResponse> = [
    new ProbeDetailResponse(2993, "DAUMFI#4", "Torso1, USB", "CW Doppler, Auto EF, Auto Doppler, TDI, Trio 2.0, PW Doppler, AI Fast", "Heart, Lungs Torso, Abdomen", "mohit", "eic_test", "*******", "iOS", "18.2.1", 1740119282001, ProductStatusEnum.DISABLED, "Algeria", true, true),
    new ProbeDetailResponse(4117, "DAUMFI-3", "Torso1, USB", "CW Doppler", "Heart, Lungs Torso, Abdomen", "testasdf", "eic_test", "********", "Android", "13", 1740052274174, ProductStatusEnum.DISABLED, "Argentina", false, true),
    new ProbeDetailResponse(4090, "DAUR2004016-002", "Torso3", "AI Fast", "Heart, Lungs Torso, Abdomen", "mohit", "eic_test", "********", "Bridge", "********", 1738560235309, ProductStatusEnum.DISABLED, "Akshay443", true, false),
  ];

  let mockProbeListFilterRequestBody: ProbeListFilterRequestBody;

  beforeEach(async () => {
    // Initialize subjects
    commonLoadingSubject = new Subject<boolean>();
    isLoadingSubject = new Subject<boolean>();
    downloadZipFileSubject = new Subject<boolean>();
    probeListFilterSubject = new Subject<ProbeFilterAction>();
    probeListLoadingSubject = new Subject<boolean>();

    // Create all spy objects
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info', 'clear']);
    localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['confirm', 'getBasicModelConfigForDisableAction', 'getBasicModelConfigForRMAAction']);
    updateAssociationServiceMock = jasmine.createSpyObj('UpdateAssociationService', ['openUpdateAssociationModel']);
    permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getProbPermission']);
    probeApiServiceSpy = jasmine.createSpyObj('ProbeApiService', ['dowloadSasUriofFeatureLicenseAsync']);
    authServiceSpy = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    countryCacheServiceSpy = jasmine.createSpyObj('CountryCacheService', ['getCountryListFromCache']);
    salesOrderApiCallServiceSpy = jasmine.createSpyObj('SalesOrderApiCallService', ['getSalesOrderNumberList']);
    presetApiServiceSpy = jasmine.createSpyObj('PresetApiService', ['getProbePresetsList']);
    customerAssociationServicespy = jasmine.createSpyObj('CustomerAssociationService', ['openCustomerAssociationPopup']);
    routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    commonsServiceSpy = jasmine.createSpyObj('CommonsService', ['accessDataSizes']);
    commonOperationsServiceSpy = jasmine.createSpyObj('CommonOperationsService', [
      'accessProbeOperations',
      'getCommonLoadingSubject'
    ]);
    downloadServiceSpy = jasmine.createSpyObj('DownloadService', [
      'getisLoadingSubject',
      'getdownloadZipFileForProbSubject'
    ]);
    salesOrderPdfDownloadServiceSpy = jasmine.createSpyObj('SalesOrderPdfDownloadService', ['openSalesOrderPdfDownloadModel']);
    probeOperationServiceSpy = jasmine.createSpyObj('ProbeOperationService', [
      'getProbeListLoadingSubject',
      'getProbeListFilterRequestParameterSubject',
      'loadProbeList',
      'callRefreshPageSubject',
      'updateCacheInBackground',
      'changeOperationForProbe',
      'getProbeListRefreshSubject',
      'clearAllFiltersAndRefresh'
    ]);
    keyValueMappingServiceSpy = jasmine.createSpyObj('KeyValueMappingServiceService', ['enumOptionToList', 'lockedUnlockOptionList', 'editEnableDisableOptionList']);
    deviceServiceSpy = jasmine.createSpyObj('DeviceService', ['method']);
    jobServiceSpy = jasmine.createSpyObj('JobService', ['method']);

    // Configure spy return values
    authServiceSpy.isAuthenticate.and.returnValue(true);
    commonsServiceSpy.accessDataSizes.and.returnValue(['10', '25', '50', '100']);
    commonOperationsServiceSpy.accessProbeOperations.and.returnValue(['Operation1', 'Operation2']);
    commonOperationsServiceSpy.getCommonLoadingSubject.and.returnValue(commonLoadingSubject);
    downloadServiceSpy.getisLoadingSubject.and.returnValue(isLoadingSubject);
    downloadServiceSpy.getdownloadZipFileForProbSubject.and.returnValue(downloadZipFileSubject);
    probeOperationServiceSpy.getProbeListLoadingSubject.and.returnValue(probeListLoadingSubject);
    probeOperationServiceSpy.getProbeListFilterRequestParameterSubject.and.returnValue(probeListFilterSubject);
    permissionServiceSpy.getProbPermission.and.returnValue(true);
    keyValueMappingServiceSpy.enumOptionToList.and.returnValue([{ key: 'ACTIVE', value: 'Active' }]);
    salesOrderPdfDownloadServiceSpy.openSalesOrderPdfDownloadModel.and.returnValue(Promise.resolve(true));
    probeApiServiceSpy.dowloadSasUriofFeatureLicenseAsync.and.returnValue(Promise.resolve());

    // Mock probe operation service loadProbeList
    probeOperationServiceSpy.loadProbeList.and.returnValue(Promise.resolve({
      success: true,
      probes: content,
      totalProbeDisplay: 3,
      totalProbes: 10,
      localProbeList: content,
      totalItems: 10,
      page: 1
    }));

    mockProbeListFilterRequestBody = null

    await TestBed.configureTestingModule({
      declarations: [
        OtsProbesComponent,
        FeaturesTextDisplayPipe,
        CommonBooleanValueDisplayPipe,
        FeaturesValidityPartNumberDisplayPipe,
        TransferOrderModuleComponent,
        FeaturesCheckBoxPipe,
        FeaturesExpireDateDisplayPipe,
        FeaturesRadioButtonPipe,
        FeaturesCustomEndDateDisplayPipe,
        AssignConfigDisablePipe,
        FeaturesBaseResponseDisplayPipe,
        FeatureValidityOptionHideShowPipe,
        EnumMappingDisplayNamePipe,
        OtsProbesDetailComponent,
        CreateUpdateMultipleProbeComponent,
        OtsProbesFilterComponent
      ],
      imports: [NgbPaginationModule, NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule, FormsModule],
      providers: [
        ExceptionHandlingService,
        DatePipe,
        { provide: JobService, useValue: jobServiceSpy },
        { provide: DeviceService, useValue: deviceServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: CommonsService, useValue: commonsServiceSpy },
        { provide: PermissionService, useValue: permissionServiceSpy },
        { provide: AuthJwtService, useValue: authServiceSpy },
        { provide: DownloadService, useValue: downloadServiceSpy },
        { provide: CommonOperationsService, useValue: commonOperationsServiceSpy },
        { provide: SalesOrderPdfDownloadService, useValue: salesOrderPdfDownloadServiceSpy },
        { provide: ProbeApiService, useValue: probeApiServiceSpy },
        { provide: ProbeOperationService, useValue: probeOperationServiceSpy },
        { provide: KeyValueMappingServiceService, useValue: keyValueMappingServiceSpy },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        { provide: UpdateAssociationService, useValue: updateAssociationServiceMock },
        { provide: PresetApiService, useValue: presetApiServiceSpy },
        { provide: CountryCacheService, useValue: countryCacheServiceSpy },
        { provide: SalesOrderApiCallService, useValue: salesOrderApiCallServiceSpy },
        { provide: CustomerAssociationService, useValue: customerAssociationServicespy },
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        commonsProviders(toastrServiceMock)
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(OtsProbesComponent);
    component = fixture.componentInstance;
    component.probeListFilterRequestBody = mockProbeListFilterRequestBody;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should initialize component when user is authenticated', () => {
      authServiceSpy.isAuthenticate.and.returnValue(true);
      permissionServiceSpy.getProbPermission.and.returnValue(true);

      component.ngOnInit();

      expect(component.page).toBe(0);
      expect(component.displayOts).toBe(true);
      expect(component.displayOTSDetail).toBe(false);
      expect(component.itemsPerPage).toBe(ITEMS_PER_PAGE);
      expect(component.drpselectsize).toBe(ITEMS_PER_PAGE);
      expect(component.previousPage).toBe(1);
    });

    it('should navigate to login when user is not authenticated', () => {
      authServiceSpy.isAuthenticate.and.returnValue(false);

      component.ngOnInit();

      expect(authServiceSpy.loginNavigate).toHaveBeenCalled();
    });

    it('should not call getProbeData when user does not have add probe permission', () => {
      authServiceSpy.isAuthenticate.and.returnValue(true);
      permissionServiceSpy.getProbPermission.and.returnValue(false);
      spyOn(component, 'getProbeData');

      component.ngOnInit();

      expect(component.getProbeData).not.toHaveBeenCalled();
    });
  });

  describe('Subject Subscriptions', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should handle probe list loading subject', () => {
      probeListLoadingSubject.next(true);
      expect(component.loading).toBe(true);

      probeListLoadingSubject.next(false);
      expect(component.loading).toBe(false);
    });

    it('should handle common loading subject', () => {
      commonLoadingSubject.next(true);
      expect(component.loading).toBe(true);

      commonLoadingSubject.next(false);
      expect(component.loading).toBe(false);
    });

    it('should handle download loading subject', () => {
      isLoadingSubject.next(true);
      expect(component.loading).toBe(true);

      isLoadingSubject.next(false);
      expect(component.loading).toBe(false);
    });

    it('should handle download zip file subject', async () => {
      spyOn(component, 'downloadProbes');

      downloadZipFileSubject.next(true);

      expect(component.downloadProbes).toHaveBeenCalledWith(true, false);
    });

    it('should handle probe list filter subject with reload data', () => {
      const mockFilterAction = new ProbeFilterAction(
        new ListingPageReloadSubjectParameter(true, true, false, false),
        mockProbeListFilterRequestBody
      );
      spyOn(component, 'loadAll');

      probeListFilterSubject.next(mockFilterAction);

      expect(component.probeIdList).toEqual([]);
      expect(component.selectedProbeDetailResponseList).toEqual([]);
      expect(component.loadAll).toHaveBeenCalledWith(mockProbeListFilterRequestBody);
    });

    it('should handle probe list filter subject without default page number', () => {
      const mockFilterAction = new ProbeFilterAction(
        new ListingPageReloadSubjectParameter(true, false, false, false),
        mockProbeListFilterRequestBody
      );
      spyOn(component, 'loadAll');

      probeListFilterSubject.next(mockFilterAction);

      expect(component.loadAll).toHaveBeenCalledWith(mockProbeListFilterRequestBody);
    });
  });

  describe('loadAll', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should load probe data successfully', async () => {
      const mockResult = {
        success: true,
        probes: content,
        totalProbeDisplay: 3,
        totalProbes: 10,
        localProbeList: content,
        totalItems: 10,
        page: 1
      };
      probeOperationServiceSpy.loadProbeList.and.returnValue(Promise.resolve(mockResult));
      spyOn(component, 'defaultSelectAll');

      await component.loadAll(mockProbeListFilterRequestBody);

      expect(component.probes).toEqual(content);
      expect(component.totalProbeDisplay).toBe(3);
      expect(component.totalProbes).toBe(10);
      expect(component.localProbeDetailResponseList).toEqual(content);
      expect(component.totalItems).toBe(10);
      expect(component.page).toBe(1);
      expect(component.defaultSelectAll).toHaveBeenCalled();
    });

    it('should handle load failure', async () => {
      const mockResult = {
        success: false,
        probes: [],
        totalProbeDisplay: 0,
        totalProbes: 0,
        localProbeList: [],
        totalItems: 0,
        page: 0
      };
      probeOperationServiceSpy.loadProbeList.and.returnValue(Promise.resolve(mockResult));
      spyOn(component, 'defaultSelectAll');

      await component.loadAll(mockProbeListFilterRequestBody);

      expect(component.probes).toEqual([]);
      expect(component.totalProbeDisplay).toBe(0);
      expect(component.totalProbes).toBe(0);
      expect(component.localProbeDetailResponseList).toEqual([]);
      expect(component.totalItems).toBe(0);
      expect(component.defaultSelectAll).toHaveBeenCalled();
    });
  });

  describe('Input/Output methods', () => {
    it('should update probeListFilterRequestBody', () => {
      spyOn(component.probeListFilterRequestBodyChange, 'emit');
      const newFilterBody = null;

      component.updateProbeListFilterRequestBody(newFilterBody);

      expect(component.probeListFilterRequestBody).toBe(newFilterBody);
      expect(component.probeListFilterRequestBodyChange.emit).toHaveBeenCalledWith(newFilterBody);
    });

    it('should update isFilterHidden', () => {
      spyOn(component.isFilterHiddenChange, 'emit');

      component.updateIsFilterHidden(true);

      expect(component.isFilterHidden).toBe(true);
      expect(component.isFilterHiddenChange.emit).toHaveBeenCalledWith(true);
    });

    it('should update isFilterComponentInitWithApicall', () => {
      spyOn(component.isFilterComponentInitWithApicallChange, 'emit');

      component.updateIsFilterComponentInitWithApicall(false);

      expect(component.isFilterComponentInitWithApicall).toBe(false);
      expect(component.isFilterComponentInitWithApicallChange.emit).toHaveBeenCalledWith(false);
    });
  });

  describe('changeDataSize', () => {
    it('should change data size and reload data', () => {
      const mockEvent = { target: { value: '25' } };
      spyOn(component, 'loadAll');

      component.changeDataSize(mockEvent);

      expect(component.loading).toBe(true);
      expect(component.probeIdList).toEqual([]);
      expect(component.selectedProbeDetailResponseList).toEqual([]);
      expect(component.itemsPerPage).toBe('25');
      expect(component.loadAll).toHaveBeenCalledWith(component.probeListFilterRequestBody);
    });
  });

  describe('Page navigation', () => {
    it('should load page when page number is different', () => {
      component.previousPage = 1;
      spyOn(component, 'transition');

      component.loadPage(2);

      expect(component.previousPage).toBe(2);
      expect(component.transition).toHaveBeenCalled();
    });

    it('should not load page when page number is same', () => {
      component.previousPage = 1;
      spyOn(component, 'transition');

      component.loadPage(1);

      expect(component.transition).not.toHaveBeenCalled();
    });

    it('should handle transition', () => {
      // Mock DOM element
      const mockElement = document.createElement('input');
      mockElement.id = 'selectAllProbe';
      mockElement.type = 'checkbox';
      spyOn(document, 'getElementById').and.returnValue(mockElement);
      spyOn(component, 'loadAll');

      component.transition();

      expect(component.loading).toBe(true);
      expect(mockElement.checked).toBe(false);
      expect(component.loadAll).toHaveBeenCalledWith(component.probeListFilterRequestBody);
    });

    it('should handle transition when element is null', () => {
      spyOn(document, 'getElementById').and.returnValue(null);
      spyOn(component, 'loadAll');

      component.transition();

      expect(component.loading).toBe(true);
      expect(component.loadAll).toHaveBeenCalledWith(component.probeListFilterRequestBody);
    });
  });

  describe('Probe selection', () => {
    beforeEach(() => {
      component.localProbeDetailResponseList = content;
    });

    it('should set local probe list', () => {
      spyOn(component, 'defaultSelectAll');

      component.setLocalProbe(content);

      expect(component.localProbeDetailResponseList).toEqual(content);
      expect(component.defaultSelectAll).toHaveBeenCalled();
    });

    it('should return true for selected probe', () => {
      component.probeIdList = [2993];

      const result = component.defaultSelectProbe(2993);

      expect(result).toBe(true);
    });

    it('should return false for unselected probe', () => {
      component.probeIdList = [];

      const result = component.defaultSelectProbe(2993);

      expect(result).toBe(false);
    });

    it('should add probe on selection', () => {
      const mockEvent = { target: { checked: true } };
      const probe = content[0];
      spyOn(component, 'defaultSelectAll');

      component.onChangeProbe(probe, mockEvent);

      expect(component.probeIdList).toContain(probe.id);
      expect(component.selectedProbeDetailResponseList).toContain(probe);
      expect(component.defaultSelectAll).toHaveBeenCalled();
    });

    it('should remove probe on deselection', () => {
      const mockEvent = { target: { checked: false } };
      const probe = content[0];
      component.probeIdList = [probe.id];
      component.selectedProbeDetailResponseList = [probe];
      spyOn(component, 'defaultSelectAll');

      component.onChangeProbe(probe, mockEvent);

      expect(component.probeIdList).not.toContain(probe.id);
      expect(component.selectedProbeDetailResponseList).not.toContain(probe);
      expect(component.defaultSelectAll).toHaveBeenCalled();
    });

    it('should clear all probe selections', () => {
      component.probeIdList = [1, 2, 3];
      component.selectedProbeDetailResponseList = content;

      // Mock DOM elements
      const mockCheckboxes = [
        { checked: true } as HTMLInputElement,
        { checked: true } as HTMLInputElement
      ];
      const mockSelectAll = { checked: true } as HTMLInputElement;

      spyOn(document, 'getElementsByName').and.returnValue(mockCheckboxes as any);
      spyOn(document, 'getElementById').and.returnValue(mockSelectAll);

      component.clearProbeIdCheckBox();

      expect(component.probeIdList).toEqual([]);
      expect(component.selectedProbeDetailResponseList).toEqual([]);
      expect(mockCheckboxes[0].checked).toBe(false);
      expect(mockCheckboxes[1].checked).toBe(false);
      expect(mockSelectAll.checked).toBe(false);
    });

    it('should handle clearProbeIdCheckBox when selectAll element is null', () => {
      component.probeIdList = [1, 2, 3];
      const mockCheckboxes = [{ checked: true } as HTMLInputElement];

      spyOn(document, 'getElementsByName').and.returnValue(mockCheckboxes as any);
      spyOn(document, 'getElementById').and.returnValue(null);

      component.clearProbeIdCheckBox();

      expect(component.probeIdList).toEqual([]);
      expect(component.selectedProbeDetailResponseList).toEqual([]);
    });

    it('should set selectAll checkbox to true when all probes are selected', () => {
      component.probeIdList = [2993, 4117, 4090];
      component.localProbeDetailResponseList = content;
      const mockSelectAll = { checked: false } as HTMLInputElement;
      spyOn(document, 'getElementById').and.returnValue(mockSelectAll);

      component.defaultSelectAll();

      expect(mockSelectAll.checked).toBe(true);
    });

    it('should set selectAll checkbox to false when not all probes are selected', () => {
      component.probeIdList = [2993];
      component.localProbeDetailResponseList = content;
      const mockSelectAll = { checked: true } as HTMLInputElement;
      spyOn(document, 'getElementById').and.returnValue(mockSelectAll);

      component.defaultSelectAll();

      expect(mockSelectAll.checked).toBe(false);
    });

    it('should handle defaultSelectAll when element is null', () => {
      component.probeIdList = [2993];
      component.localProbeDetailResponseList = content;
      spyOn(document, 'getElementById').and.returnValue(null);

      expect(() => component.defaultSelectAll()).not.toThrow();
    });

    it('should select all probes on current page', () => {
      const mockEvent = { target: { checked: true } };
      const mockCheckboxes = [
        { checked: false } as HTMLInputElement,
        { checked: false } as HTMLInputElement
      ];
      component.localProbeDetailResponseList = content.slice(0, 2);
      component.probeIdList = [];
      component.selectedProbeDetailResponseList = [];

      spyOn(document, 'getElementsByName').and.returnValue(mockCheckboxes as any);

      component.selectAllProbe(mockEvent);

      expect(mockCheckboxes[0].checked).toBe(true);
      expect(mockCheckboxes[1].checked).toBe(true);
      expect(component.probeIdList.length).toBe(2);
      expect(component.selectedProbeDetailResponseList.length).toBe(2);
    });

    it('should deselect all probes on current page', () => {
      const mockEvent = { target: { checked: false } };
      const mockCheckboxes = [
        { checked: true } as HTMLInputElement,
        { checked: true } as HTMLInputElement
      ];
      component.localProbeDetailResponseList = content.slice(0, 2);
      component.probeIdList = [2993, 4117];
      component.selectedProbeDetailResponseList = content.slice(0, 2);

      spyOn(document, 'getElementsByName').and.returnValue(mockCheckboxes as any);

      component.selectAllProbe(mockEvent);

      expect(mockCheckboxes[0].checked).toBe(false);
      expect(mockCheckboxes[1].checked).toBe(false);
      expect(component.probeIdList).toEqual([]);
      expect(component.selectedProbeDetailResponseList).toEqual([]);
    });

    it('should not add duplicate probes when selecting all', () => {
      const mockEvent = { target: { checked: true } };
      const mockCheckboxes = [{ checked: false } as HTMLInputElement];
      component.localProbeDetailResponseList = [content[0]];
      component.probeIdList = [content[0].id]; // Already selected
      component.selectedProbeDetailResponseList = [content[0]];

      spyOn(document, 'getElementsByName').and.returnValue(mockCheckboxes as any);

      component.selectAllProbe(mockEvent);

      expect(component.probeIdList.length).toBe(1);
      expect(component.selectedProbeDetailResponseList.length).toBe(1);
    });
  });

  describe('Filter and Operations', () => {
    it('should toggle filter visibility to hidden', () => {
      component.isFilterHidden = false;
      spyOn(component, 'updateIsFilterComponentInitWithApicall');
      spyOn(component, 'updateIsFilterHidden');

      component.toggleFilter();

      expect(component.updateIsFilterComponentInitWithApicall).toHaveBeenCalledWith(false);
      expect(component.updateIsFilterHidden).toHaveBeenCalledWith(true);
      expect(component.hideShowFilterButtonText).toBe(collapseFilterTextEnum.HIDE_FILTER);
    });

    it('should toggle filter visibility to visible', () => {
      component.isFilterHidden = true;
      spyOn(component, 'updateIsFilterComponentInitWithApicall');
      spyOn(component, 'updateIsFilterHidden');

      component.toggleFilter();

      expect(component.updateIsFilterComponentInitWithApicall).toHaveBeenCalledWith(false);
      expect(component.updateIsFilterHidden).toHaveBeenCalledWith(false);
      expect(component.hideShowFilterButtonText).toBe(collapseFilterTextEnum.SHOW_FILTER);
    });

    it('should refresh filter', () => {
      spyOn(component, 'filterPageSubjectCallForReloadPage');

      component.refreshFilter();

      expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalledWith(true, false);
    });

    it('should call refresh page subject', () => {
      component.filterPageSubjectCallForReloadPage(true, false);

      expect(probeOperationServiceSpy.callRefreshPageSubject).toHaveBeenCalledWith(
        jasmine.any(ListingPageReloadSubjectParameter),
        ProbListResource,
        component.isFilterHidden,
        component.probeListFilterRequestBody
      );
    });

    it('should handle refresh button click', () => {
      spyOn(component, 'refreshFilter');

      component.clickOnRefreshButton();

      expect(probeOperationServiceSpy.updateCacheInBackground).toHaveBeenCalled();
      expect(component.refreshFilter).toHaveBeenCalled();
    });

    it('should change probe operation', () => {
      const mockEvent = { target: { value: 'TEST_OPERATION' } };
      const mockSelection = { value: '' } as HTMLSelectElement;
      component.probeIdList = [1, 2, 3];
      component.selectedProbeDetailResponseList = content;

      spyOn(document, 'getElementById').and.returnValue(mockSelection);

      component.changeProbeOperation(mockEvent);

      expect(probeOperationServiceSpy.changeOperationForProbe).toHaveBeenCalledWith(
        'TEST_OPERATION',
        ProbListResource,
        component.probeIdList,
        component.selectedProbeDetailResponseList
      );
      expect(mockSelection.value).toBe(ProbeOperationsEnum.Probe_Operations);
    });
  });

  describe('Navigation and Display', () => {
    it('should open probe detail', () => {
      const probe = { id: 123 };
      spyOn(component.showProbeDetail, 'emit');

      component.openProbeDetail(probe);

      expect(component.probeId).toBe(123);
      expect(component.showProbeDetail.emit).toHaveBeenCalledWith(123);
    });

    it('should show OTS probe list', () => {
      spyOn(component, 'loadAll');
      component.displayOtsProbeAddUpdate = true;
      component.displayOTSDetail = true;
      component.probeIdList = [1, 2, 3];
      component.displayOts = false;

      component.showOtsProbe();

      expect(component.displayOtsProbeAddUpdate).toBe(false);
      expect(component.displayOTSDetail).toBe(false);
      expect(component.probeIdList).toEqual([]);
      expect(component.displayOts).toBe(true);
      expect(component.loadAll).toHaveBeenCalledWith(component.probeListFilterRequestBody);
    });

    it('should show OTS probe add/update', () => {
      component.displayOtsProbeAddUpdate = false;
      component.displayOTSDetail = true;
      component.displayOts = true;

      component.otsProbeAddUpdate();

      expect(component.displayOtsProbeAddUpdate).toBe(true);
      expect(component.displayOTSDetail).toBe(false);
      expect(component.displayOts).toBe(false);
    });
  });

  describe('Download and PDF', () => {
    it('should download probes when model status is true', async () => {
      component.probeIdList = [1, 2, 3];
      spyOn(component, 'loadAll');
      spyOn(component, 'clearProbeIdCheckBox');

      await component.downloadProbes(true, true);

      expect(component.loading).toBe(true);
      expect(probeApiServiceSpy.dowloadSasUriofFeatureLicenseAsync).toHaveBeenCalledWith(
        component.probeIdList,
        ProbListResource
      );
      expect(component.loadAll).toHaveBeenCalledWith(component.probeListFilterRequestBody);
      expect(component.clearProbeIdCheckBox).toHaveBeenCalled();
    });

    it('should not download probes when model status is false', async () => {
      spyOn(component, 'clearProbeIdCheckBox');

      await component.downloadProbes(false, false);

      expect(component.loading).toBe(false);
      expect(probeApiServiceSpy.dowloadSasUriofFeatureLicenseAsync).not.toHaveBeenCalled();
      expect(component.clearProbeIdCheckBox).toHaveBeenCalled();
    });

    it('should download PDF', () => {
      component.downloadPdf();

      expect(salesOrderPdfDownloadServiceSpy.openSalesOrderPdfDownloadModel).toHaveBeenCalled();
    });
  });

  describe('Permission and Initialization', () => {
    it('should set permissions correctly', () => {
      permissionServiceSpy.getProbPermission.and.returnValues(false, true, true);

      component['setProbPermission']();

      expect(component.probAdminPermission).toBe(false);
      expect(component.addProbPermission).toBe(false);
      expect(component.downloadSalesOrderLetterPermission).toBe(true);
    });

    it('should get probe data and initialize', () => {
      spyOn(component, 'updateIsFilterHidden');
      spyOn(component, 'updateIsFilterComponentInitWithApicall');
      spyOn(component, 'clearProbeIdCheckBox');

      component.getProbeData();

      expect(component.page).toBe(0);
      expect(component.drpselectsize).toBe(ITEMS_PER_PAGE);
      expect(component.itemsPerPage).toBe(ITEMS_PER_PAGE);
      expect(component.updateIsFilterComponentInitWithApicall).toHaveBeenCalledWith(true);
      expect(component.clearProbeIdCheckBox).toHaveBeenCalled();
    });

    it('should initialize product status list', () => {
      const mockStatusList = [{ key: 'ACTIVE', value: 'Active' }];
      keyValueMappingServiceSpy.enumOptionToList.and.returnValue(mockStatusList);

      component['initializeProductStatusList']();

      expect(keyValueMappingServiceSpy.enumOptionToList).toHaveBeenCalledWith(ProductStatusEnum);
      expect(component.productStatusList).toEqual(mockStatusList);
    });
  });

  describe('Private methods', () => {
    it('should reset page', () => {
      component.page = 5;
      component.previousPage = 6;

      component['resetPage']();

      expect(component.page).toBe(0);
      expect(component.previousPage).toBe(1);
    });

    it('should set loading status', () => {
      component['setLoadingStatus'](true);
      expect(component.loading).toBe(true);

      component['setLoadingStatus'](false);
      expect(component.loading).toBe(false);
    });

    it('should get parameter for device historical data', () => {
      const result = component['getParameterForDeviceHistoricalData']();
      expect(result).toBe(DeviceHistoricalData.NORMAL);
    });
  });

  describe('ngOnDestroy', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should unsubscribe from all subscriptions', () => {
      // Create mock subscriptions
      component['subscriptionForCommonloading'] = jasmine.createSpyObj('Subscription', ['unsubscribe']);
      component['subscriptionForisloading'] = jasmine.createSpyObj('Subscription', ['unsubscribe']);
      component['subscriptionForDownloadZipFileProbSubject'] = jasmine.createSpyObj('Subscription', ['unsubscribe']);
      component['subscriptionForProbeListFilterRequestParameter'] = jasmine.createSpyObj('Subscription', ['unsubscribe']);
      component['subscriptionForProbeListLoading'] = jasmine.createSpyObj('Subscription', ['unsubscribe']);

      component.ngOnDestroy();

      expect(component['subscriptionForCommonloading'].unsubscribe).toHaveBeenCalled();
      expect(component['subscriptionForisloading'].unsubscribe).toHaveBeenCalled();
      expect(component['subscriptionForDownloadZipFileProbSubject'].unsubscribe).toHaveBeenCalled();
      expect(component['subscriptionForProbeListFilterRequestParameter'].unsubscribe).toHaveBeenCalled();
      expect(component['subscriptionForProbeListLoading'].unsubscribe).toHaveBeenCalled();
    });

    it('should handle undefined subscriptions gracefully', () => {
      component['subscriptionForCommonloading'] = undefined;
      component['subscriptionForisloading'] = undefined;
      component['subscriptionForDownloadZipFileProbSubject'] = undefined;
      component['subscriptionForProbeListFilterRequestParameter'] = undefined;
      component['subscriptionForProbeListLoading'] = undefined;

      expect(() => component.ngOnDestroy()).not.toThrow();
    });
  });

  describe('Edge cases and error handling', () => {
    it('should handle empty probe list in setLocalProbe', () => {
      spyOn(component, 'defaultSelectAll');

      component.setLocalProbe([]);

      expect(component.localProbeDetailResponseList).toEqual([]);
      expect(component.defaultSelectAll).toHaveBeenCalled();
    });

    it('should handle probe operation change with null event value', () => {
      const mockEvent = { target: { value: null } };
      const mockSelection = { value: '' } as HTMLSelectElement;
      spyOn(document, 'getElementById').and.returnValue(mockSelection);

      component.changeProbeOperation(mockEvent);

      expect(probeOperationServiceSpy.changeOperationForProbe).toHaveBeenCalledWith(
        null,
        ProbListResource,
        component.probeIdList,
        component.selectedProbeDetailResponseList
      );
    });

    it('should handle transition with loading already true', () => {
      component.loading = true;
      spyOn(component, 'loadAll');
      spyOn(document, 'getElementById').and.returnValue(null);

      component.transition();

      expect(component.loading).toBe(true);
      expect(component.loadAll).toHaveBeenCalled();
    });

    it('should handle empty probeIdList in operations', () => {
      component.probeIdList = [];
      component.selectedProbeDetailResponseList = [];
      const mockEvent = { target: { value: 'TEST_OPERATION' } };
      const mockSelection = { value: '' } as HTMLSelectElement;
      spyOn(document, 'getElementById').and.returnValue(mockSelection);

      component.changeProbeOperation(mockEvent);

      expect(probeOperationServiceSpy.changeOperationForProbe).toHaveBeenCalledWith(
        'TEST_OPERATION',
        ProbListResource,
        [],
        []
      );
    });
  });

  describe('Constants and Properties', () => {
    it('should have correct constant values', () => {
      expect(component.probeListResource).toBe(ProbListResource);
      expect(component.drpselectsize).toBe(ITEMS_PER_PAGE);
      expect(component.dateFormat).toBe(DateTimeDisplayFormat);
      expect(component.showEntry).toBe(SHOW_ENTRY);
      expect(component.dateTimeDisplayFormat).toBe(DateTimeDisplayFormat);
    });

    it('should initialize with correct default values', () => {
      expect(component.probeIdList).toEqual([]);
      expect(component.localProbeDetailResponseList).toEqual([]);
      expect(component.selectedProbeDetailResponseList).toEqual([]);
      expect(component.page).toBe(0);
      expect(component.loading).toBe(false);
      expect(component.totalProbeDisplay).toBe(0);
      expect(component.totalProbes).toBe(0);
      expect(component.displayOts).toBe(true);
      expect(component.displayOTSDetail).toBe(false);
      expect(component.displayOtsProbeAddUpdate).toBe(false);
      expect(component.hideShowFilterButtonText).toBe(collapseFilterTextEnum.HIDE_FILTER);
      expect(component.probAdminPermission).toBe(false);
      expect(component.addProbPermission).toBe(true);
      expect(component.downloadSalesOrderLetterPermission).toBe(false);
      expect(component.isAddProbeBtnDisplay).toBe(true);
    });
  });

  describe('Array operations', () => {
    it('should handle probe selection with existing selection', () => {
      const probe = content[0];
      const mockEvent = { target: { checked: false } };
      component.probeIdList = [probe.id, 999];
      component.selectedProbeDetailResponseList = [probe, content[1]];
      spyOn(component, 'defaultSelectAll');

      component.onChangeProbe(probe, mockEvent);

      expect(component.probeIdList).toEqual([999]);
      expect(component.selectedProbeDetailResponseList).toEqual([content[1]]);
      expect(component.defaultSelectAll).toHaveBeenCalled();
    });

    it('should handle selectAllProbe deselection with partial selection', () => {
      const mockEvent = { target: { checked: false } };
      const mockCheckboxes = [
        { checked: true } as HTMLInputElement,
        { checked: true } as HTMLInputElement
      ];

      component.localProbeDetailResponseList = [content[0], content[1]];
      component.probeIdList = [content[0].id, content[1].id, 999]; // Extra ID not in local list
      component.selectedProbeDetailResponseList = [content[0], content[1], content[2]]; // Extra probe

      spyOn(document, 'getElementsByName').and.returnValue(mockCheckboxes as any);

      component.selectAllProbe(mockEvent);

      expect(component.probeIdList).toEqual([999]); // Should only remove local probes
      expect(component.selectedProbeDetailResponseList).toEqual([content[2]]); // Should only remove local probes
    });
  });
});